<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_followups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->dateTime('track_date');
            $table->text('note');
            $table->string('status', 50);
            $table->boolean('pluscard_been_loaded')->nullable();
            $table->integer('number_of_customers_loaded')->nullable();
            $table->decimal('loading_amount', 12, 2)->nullable();
            $table->string('reason_not_understanding')->nullable();
            $table->string('conversation_type')->nullable();
            $table->string('contact_first_name')->nullable();
            $table->string('contact_last_name')->nullable();
            $table->string('contact_title')->nullable();
            $table->string('contact_phone')->nullable();
            $table->string('contact_email')->nullable();
            $table->integer('current_branch_count')->nullable();
            $table->integer('branch_potential')->nullable();
            $table->boolean('agreement_status')->nullable();
            $table->string('city')->nullable();
            $table->string('district')->nullable();
            $table->string('branch_name')->nullable();
            $table->date('meet_date')->nullable();
            $table->string('user_name')->nullable();
            $table->string('company_name')->nullable();
            $table->string('phone_1')->nullable();
            $table->string('phone_2')->nullable();
            $table->string('phone_3')->nullable();
            $table->string('mail')->nullable();
            $table->string('work_type')->nullable();
            $table->string('current_firm')->nullable();
            $table->text('description')->nullable();
            $table->string('status_at_meeting_date')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_followups');
    }
};
