name: crm.umram.online

services:
  web:
    container_name: crmumramonline_web
    image: nginx:stable-alpine
    working_dir: "/var/www/crm.umram.online"
    environment:
      - APP_ENV=local
      - APP_DEBUG=1
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./../:/var/www/crm.umram.online:delegated
      - ./web/crm.umram.online.conf:/etc/nginx/conf.d/crm.umram.online.conf:consistent
    networks:
      - crmumramonline_net
  app:
    container_name: crmumramonline_app
    build: ./app
    working_dir: "/var/www/crm.umram.online"
    environment:
      - APP_ENV=local
      - APP_DEBUG=1
    volumes:
      - ./../:/var/www/crm.umram.online:delegated
      - ./../app/php.ini:/usr/local/etc/php/php.ini
    networks:
      - crmumramonline_net
  db:
    container_name: crmumramonline_db
    hostname: crmumramonline_db
    image: postgres
    restart: "no"
    tty: true
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
      POSTGRES_DB: crmumramdb
    volumes:
      - ./db/data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - crmumramonline_net
  adminer:
    container_name: crmumramonline_adminer
    hostname: adminer
    image: adminer
    restart: always
    ports:
      - 8080:8080
    networks:
      - crmumramonline_net
  redis:
    container_name: crmumramonline_redis
    image: "redis:alpine"
    ports:
      - 6379
    networks:
      - crmumramonline_net

networks:
  crmumramonline_net:
