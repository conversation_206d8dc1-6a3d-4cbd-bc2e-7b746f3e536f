<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PotentialCustomer extends Model
{
    protected $fillable = [
        'company_name',
        'authorized_title',
        'authorized_name',
        'authorized_lastname',
        'authorized_phone',
        'phone_1',
        'phone_2',
        'phone_3',
        'city',
        'district',
        'address',
        'offer_status',
        'offer_date',
        'is_working_with_us'
    ];

    public function scopeSearch($query, $q)
    {
        if (!$q) return $query;

        $qNoSpace = str_replace(' ', '', $q);

        return $query->where(function($sub) use ($q, $qNoSpace) {
            $sub->where('company_name', 'ILIKE', "%$q%")
                ->orWhere('authorized_title', 'ILIKE', "%$q%")
                ->orWhere('authorized_name', 'ILIKE', "%$q%")
                ->orWhere('authorized_lastname', 'ILIKE', "%$q%")
                ->orWhere('authorized_phone', 'ILIKE', "%$q%")
                ->orWhere('phone_1', 'ILIKE', "%$q%")
                ->orWhere('phone_2', 'ILIKE', "%$q%")
                ->orWhere('phone_3', 'ILIKE', "%$q%")
                ->orWhereRaw("REPLACE(phone_1, ' ', '') ILIKE ?", ["%$qNoSpace%"])
                ->orWhereRaw("REPLACE(phone_2, ' ', '') ILIKE ?", ["%$qNoSpace%"])
                ->orWhereRaw("REPLACE(phone_3, ' ', '') ILIKE ?", ["%$qNoSpace%"])
                ->orWhere('city', 'ILIKE', "%$q%")
                ->orWhere('district', 'ILIKE', "%$q%")
                ->orWhere('address', 'ILIKE', "%$q%")
                ->orWhereHas('authorizedPersons', function($personQuery) use ($q, $qNoSpace) {
                    $personQuery->where('name', 'ILIKE', "%$q%")
                        ->orWhere('lastname', 'ILIKE', "%$q%")
                        ->orWhere('title', 'ILIKE', "%$q%")
                        ->orWhere('phone', 'ILIKE', "%$q%")
                        ->orWhereRaw("REPLACE(phone, ' ', '') ILIKE ?", ["%$qNoSpace%"]);
                })
                ->orWhereHas('phones', function($phoneQuery) use ($q, $qNoSpace) {
                    $phoneQuery->where('phone', 'ILIKE', "%$q%")
                        ->orWhereRaw("REPLACE(phone, ' ', '') ILIKE ?", ["%$qNoSpace%"]);
                });
        });
    }

    public function authorizedPersons(): HasMany
    {
        return $this->hasMany(PotentialCustomerAuthorizedPerson::class);
    }

    public function phones()
    {
        return $this->hasMany(PotentialCustomerPhone::class);
    }
}

