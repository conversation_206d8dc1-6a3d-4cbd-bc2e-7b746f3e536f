<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PotentialCustomer;
use Faker\Factory as Faker;

class PotentialCustomerSeeder extends Seeder
{
    public function run(): void
    {
        $faker = Faker::create('tr_TR');
        $cities = [
            'İstanbul' => ['Kadıköy', 'Beşiktaş', 'Üsküdar', 'Beylikdüzü', 'Avcılar', 'Bakırköy', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
            'Ankara' => ['Çankaya', 'Keçiören', 'Yenimahalle', 'Mamak', 'Altındağ', 'Etimesgut', 'Sincan'],
            'İzmir' => ['<PERSON>nak', 'Karş<PERSON>ya<PERSON>', 'Bornova', 'Buca', 'Alsancak', 'Göztepe', 'Urla'],
            'Bursa' => ['Osmangazi', '<PERSON>l<PERSON>fer', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'İnegöl', '<PERSON>dan<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
            '<PERSON>talya' => ['<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Manav<PERSON>', '<PERSON><PERSON>alt<PERSON>', '<PERSON><PERSON><PERSON>', 'Döşemealtı'],
            'Adana' => ['Seyhan', 'Çukurova', 'Sarıçam', 'Yüreğir', 'Karaisalı', 'Pozantı'],
            'Konya' => ['Selçuklu', 'Meram', 'Karatay', 'Ereğli', 'Akşehir', 'Beyşehir'],
            'Gaziantep' => ['Şahinbey', 'Şehitkamil', 'Oğuzeli', 'Nizip', 'İslahiye', 'Araban']
        ];
        
        $titles = ['Genel Müdür', 'Satış Müdürü', 'İnsan Kaynakları Uzmanı', 'Pazarlama Müdürü', 'İşletme Müdürü', 'Danışman', 'Yönetici'];
        $offerStatuses = ['new', 'pending', 'accepted', 'rejected', 'customer'];
        
        for ($i = 0; $i < 15; $i++) {
            $city = $faker->randomElement(array_keys($cities));
            $district = $faker->randomElement($cities[$city]);
            
            $potentialCustomer = PotentialCustomer::create([
                'company_name' => $faker->company . ' ' . $faker->randomElement(['Ltd. Şti.', 'A.Ş.']),
                'phone_1' => $faker->numerify('+90 2## ### ## ##'),
                'phone_2' => $faker->optional(0.7)->numerify('+90 2## ### ## ##'),
                'phone_3' => $faker->optional(0.3)->numerify('+90 2## ### ## ##'),
                'city' => $city,
                'district' => $district,
                'address' => $faker->streetAddress . ', ' . $district . '/' . $city,
                'offer_status' => $faker->randomElement($offerStatuses),
                'offer_date' => $faker->optional(0.8)->dateTimeBetween('-3 months', 'now'),
                'is_working_with_us' => $faker->boolean(20), // %20 ihtimalle bizimle çalışıyor
            ]);

            $personCount = $faker->numberBetween(1, 3);
            for ($j = 0; $j < $personCount; $j++) {
                $potentialCustomer->authorizedPersons()->create([
                    'title' => $faker->randomElement($titles),
                    'name' => $faker->firstName,
                    'lastname' => $faker->lastName,
                    'phone' => $faker->numerify('+90 5## ### ## ##'),
                ]);
            }
        }
    }
}

