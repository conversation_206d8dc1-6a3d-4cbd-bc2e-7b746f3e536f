<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PotentialCustomerAuthorized<PERSON>erson extends Model
{
    protected $table = 'potential_customer_authorized_persons';
    
    protected $fillable = [
        'potential_customer_id',
        'title',
        'name',
        'lastname',
        'phone',
    ];

    public function potentialCustomer(): BelongsTo
    {
        return $this->belongsTo(PotentialCustomer::class);
    }
}
