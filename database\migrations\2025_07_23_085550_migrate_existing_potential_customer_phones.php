<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\PotentialCustomer;
use App\Models\PotentialCustomerPhone;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mevcut potential_customers tablosundaki telefon bilgilerini
        // potential_customer_phones tablosuna taşı
        $potentialCustomers = PotentialCustomer::whereNotNull('phone_1')
            ->orWhereNotNull('phone_2')
            ->orWhereNotNull('phone_3')
            ->get();

        foreach ($potentialCustomers as $potentialCustomer) {
            // phone_1 varsa ekle
            if (!empty($potentialCustomer->phone_1)) {
                PotentialCustomerPhone::create([
                    'potential_customer_id' => $potentialCustomer->id,
                    'phone' => $potentialCustomer->phone_1,
                    'type' => 'Sabit',
                ]);
            }

            // phone_2 varsa ekle
            if (!empty($potentialCustomer->phone_2)) {
                PotentialCustomerPhone::create([
                    'potential_customer_id' => $potentialCustomer->id,
                    'phone' => $potentialCustomer->phone_2,
                    'type' => 'Sabit',
                ]);
            }

            // phone_3 varsa ekle
            if (!empty($potentialCustomer->phone_3)) {
                PotentialCustomerPhone::create([
                    'potential_customer_id' => $potentialCustomer->id,
                    'phone' => $potentialCustomer->phone_3,
                    'type' => 'Sabit',
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Geri alma işlemi - potential_customer_phones tablosundaki
        // verileri potential_customers tablosuna geri taşı
        $potentialCustomers = PotentialCustomer::all();

        foreach ($potentialCustomers as $potentialCustomer) {
            $phones = $potentialCustomer->phones()->orderBy('id')->get();

            if ($phones->count() > 0) {
                $potentialCustomer->update(['phone_1' => $phones[0]->phone ?? null]);
            }
            if ($phones->count() > 1) {
                $potentialCustomer->update(['phone_2' => $phones[1]->phone ?? null]);
            }
            if ($phones->count() > 2) {
                $potentialCustomer->update(['phone_3' => $phones[2]->phone ?? null]);
            }
        }

        // Tüm telefon kayıtlarını sil
        PotentialCustomerPhone::truncate();
    }
};
