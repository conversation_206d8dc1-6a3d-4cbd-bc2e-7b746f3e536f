name: Deploy to Test Server

on:
  push:
    branches:
      - test

jobs:
  deploy:
    runs-on: self-hosted

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Deploy via SSH
        run: |
          sshpass -p "${{ secrets.SSH_PASSWORD }}" ssh -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'EOF'
            cd /var/www/testcrm.umram.online
            git pull origin test
            php artisan migrate:refresh
            php artisan db:seed
            php artisan optimize:clear
            # composer install --no-dev --prefer-dist
            # php artisan migrate --force
          EOF
