<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CustomerFollowup;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;
use Carbon\Carbon;

class CustomerFollowupSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('customer_followups')->truncate();
        $faker = Faker::create('tr_TR');
        
        $customers = Customer::all();
        
        if ($customers->isEmpty()) {
            $this->command->info('No customers found. Please run CustomerSeeder first.');
            return;
        }
        
        $conversationTypes = ['telefon', 'yerinde', 'bayide'];
        $meetTypes = ['İlk görüşme', 'Tekra<PERSON> ziyaret', 'Anlaşma görüşmesi', '<PERSON>um', 'Demo', 'Fiyat teklifi'];
        $statuses = ['Aktif', 'Pasif', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>ş<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Değerlendirme'];
        $workTypes = ['Eks<PERSON>iz', 'Danışmanlık', 'Eğitim', 'Teknik destek', 'Satış sonrası hizmet'];
        $statusAtMeetingDates = ['Olumlu', 'Olumsuz', 'Kararsız', 'Beklemede', 'Anlaşma sağlandı'];
        $reasonsNotUnderstanding = [
            'Fiyat yüksek', 'Mesafe Uzak', 'Bayi ile yaşanan sorunlar', 
            'Ekpertize ihtiyaç duymuyor', 'Kendisi yapıyor', 
            'Başka ekspertize yaptırıyor', 'Değerlendirme'
        ];
        
        foreach ($customers as $customer) {
            // Her müşteri için 1-5 takip kaydı oluştur
            $followupCount = $faker->numberBetween(1, 5);
            
            for ($i = 0; $i < $followupCount; $i++) {
                $trackDate = $faker->dateTimeBetween('-6 months', 'now');
                $meetDate = $faker->optional(0.8)->dateTimeBetween($trackDate, '+1 month');
                $conversationType = $faker->randomElement($conversationTypes);
                $pluscardBeenLoaded = $faker->boolean(30); // %30 ihtimalle yükleme yapılmış
                
                $followup = CustomerFollowup::create([
                    'customer_id' => $customer->id,
                    'track_date' => $trackDate,
                    'note' => $faker->paragraph(2),
                    'status' => $faker->randomElement($statuses),
                    'conversation_type' => $conversationType,
                    'pluscard_been_loaded' => $pluscardBeenLoaded,
                    'number_of_customers_loaded' => $pluscardBeenLoaded ? $faker->numberBetween(1, 50) : null,
                    'loading_amount' => $pluscardBeenLoaded ? $faker->randomFloat(2, 100, 10000) : null,
                    'reason_not_understanding' => !$pluscardBeenLoaded ? $faker->randomElement($reasonsNotUnderstanding) : null,
                    'contact_first_name' => $faker->firstName,
                    'contact_last_name' => $faker->lastName,
                    'contact_title' => $faker->randomElement(['Müdür', 'Yönetici', 'Satış Sorumlusu', 'Operasyon Müdürü']),
                    'contact_phone' => $faker->numerify('+90 5## ### ## ##'),
                    'contact_email' => $faker->safeEmail,
                    'current_branch_count' => $faker->numberBetween(1, 20),
                    'branch_potential' => $faker->numberBetween(1, 50),
                    'agreement_status' => $faker->optional(0.6)->boolean(),
                    
                    // Yeni alanlar
                    'city' => $customer->city,
                    'district' => $customer->district,
                    'branch_name' => $customer->company_name,
                    'meet_date' => $meetDate,
                    'user_name' => $faker->randomElement(['Ahmet Yılmaz', 'Fatma Demir', 'Mehmet Kaya', 'Ayşe Özkan', 'Ali Çelik']),
                    'company_name' => $customer->company_name,
                    'phone_1' => $customer->phone_1,
                    'phone_2' => $customer->phone_2,
                    'phone_3' => $customer->phone_3,
                    'mail' => $customer->email,
                    'work_type' => $faker->randomElement($workTypes),
                    'current_firm' => $faker->optional(0.7)->company,
                    'description' => $faker->optional(0.8)->paragraph(3),
                    'status_at_meeting_date' => $faker->randomElement($statusAtMeetingDates),
                ]);
            }
        }
        
        $this->command->info('CustomerFollowupSeeder completed successfully!');
    }
} 