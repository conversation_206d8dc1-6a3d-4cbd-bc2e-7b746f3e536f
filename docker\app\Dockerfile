FROM php:8.4-fpm-alpine

RUN apk --no-cache update \
    && apk --no-cache upgrade \
    && apk add curl unzip

#RUN apk add --no-cache \
#    build-base \
#    libressl-dev \
#    pcre-dev

RUN apk add --no-cache libzip-dev zlib-dev libpng-dev oniguruma-dev libcurl curl-dev libxml2-dev

RUN docker-php-ext-install mysqli pdo_mysql exif zip gd bcmath soap pcntl

# Redis and Xdebug
#RUN pecl install redis \
#	&& pecl install xdebug \
#	&& docker-php-ext-enable redis xdebug

# Install Composer
RUN curl -sS https://getcomposer.org/installer -o /tmp/composer-setup.php \
    && HASH=`curl -sS https://composer.github.io/installer.sig` \
    && php -r "if (hash_file('SHA384', '/tmp/composer-setup.php') === '$HASH') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;" \
    && php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer

# Temizlik
RUN apk del \
    gcc \
    g++ \
    make \
    autoconf \
    build-base libressl-dev pcre-dev \
    && rm -rf /tmp/pear \
    && rm -rf /var/cache/apk/*

RUN apk add --no-cache postgresql-dev \
    && docker-php-ext-install pdo pdo_pgsql pgsql