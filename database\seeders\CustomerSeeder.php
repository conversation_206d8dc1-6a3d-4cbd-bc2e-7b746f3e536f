<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Customer;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\DB;

class CustomerSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('customers')->truncate();
        $faker = Faker::create('tr_TR');
        $cities = [
            'İstanbul' => ['Kadıköy', 'Beşiktaş', 'Üsküdar', 'Beylikdüzü', '<PERSON>v<PERSON><PERSON><PERSON>', 'Bakırköy', '<PERSON><PERSON><PERSON><PERSON>', 'Fatih'],
            'Ankara' => ['<PERSON>an<PERSON>', 'Keçiören', 'Yenimahalle', 'Mamak', 'Altındağ', 'Etimesgut', 'Sincan'],
            'İzmir' => ['Konak', 'Karşıyaka', 'Bornova', 'Buca', 'Alsancak', 'Göztepe', '<PERSON>rla'],
            'Bursa' => ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>m', '<PERSON><PERSON>g<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
            '<PERSON><PERSON><PERSON>' => ['<PERSON>ratpaş<PERSON>', 'Ke<PERSON>z', 'Alanya', 'Manavgat', 'Konyaaltı', 'Aksu', 'Döşemealtı'],
            'Adana' => ['Seyhan', 'Çukurova', 'Sarıçam', 'Yüreğir', 'Karaisalı', 'Pozantı'],
            'Konya' => ['Selçuklu', 'Meram', 'Karatay', 'Ereğli', 'Akşehir', 'Beyşehir'],
            'Gaziantep' => ['Şahinbey', 'Şehitkamil', 'Oğuzeli', 'Nizip', 'İslahiye', 'Araban']
        ];
        
        $titles = ['Genel Müdür', 'Satış Müdürü', 'Operasyon Müdürü', 'Pazarlama Müdürü', 'İşletme Müdürü', 'Bölge Müdürü', 'Şube Müdürü', 'Yönetici'];
        
        for ($i = 0; $i < 20; $i++) {
            $city = $faker->randomElement(array_keys($cities));
            $district = $faker->randomElement($cities[$city]);
            $firstName = $faker->firstName;
            $lastName = $faker->lastName;
            
            Customer::create([
                'email' => $faker->unique()->safeEmail,
                'tc' => $faker->unique()->numerify('###########'),
                'pluscard_no' => $faker->unique()->numerify('################'),
                'authorized_title' => $faker->randomElement($titles),
                'authorized_first_name' => $firstName,
                'authorized_last_name' => $lastName,
                'authorized_phone' => $faker->numerify('+90 5## ### ## ##'),
                'company_name' => $faker->company . ' ' . $faker->randomElement(['Ltd. Şti.', 'A.Ş.', 'Sanayi ve Ticaret Ltd. Şti.', 'Ticaret A.Ş.']),
                'phone_1' => $faker->numerify('+90 2## ### ## ##'),
                'phone_2' => $faker->optional(0.7)->numerify('+90 2## ### ## ##'),
                'phone_3' => $faker->optional(0.3)->numerify('+90 2## ### ## ##'),
                'city' => $city,
                'district' => $district,
                'address' => $faker->streetAddress . ', ' . $district . '/' . $city,
            ]);
        }
    }
} 