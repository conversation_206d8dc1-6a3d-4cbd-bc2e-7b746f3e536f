<?php

namespace App\Http\Controllers;

use App\Models\CustomerFollowup;
use App\Models\Customer;
use Illuminate\Http\Request;

class CustomerFollowupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index($customer_id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followups = $customer->customerFollowups()->orderBy('track_date', 'desc')->paginate(10);
        return view('customer_followups.index', compact('customer', 'followups'));
    }

    /**
     * Display all customer followups.
     */
    public function allFollowups(Request $request)
    {
        $query = CustomerFollowup::with('customer');

        // Filtreler
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('conversation_type')) {
            $query->where('conversation_type', $request->conversation_type);
        }

        if ($request->filled('date_from')) {
            $query->where('track_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('track_date', '<=', $request->date_to);
        }

        $followups = $query->orderBy('track_date', 'desc')->paginate(15);
        return view('customer_followups.all_followups', compact('followups'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($customer_id)
    {
        $customer = Customer::findOrFail($customer_id);
        $authUser = auth()->user();
        
        // Pre-fill data from customer and authenticated user
        $prefill = [
            'city' => $customer->city,
            'district' => $customer->district,
            'branch_name' => $customer->company_name, // Using company_name as branch_name
            'user_name' => $authUser ? $authUser->name : '',
            'company_name' => $customer->company_name,
            'phone_1' => $customer->phone_1,
            'phone_2' => $customer->phone_2,
            'phone_3' => $customer->phone_3,
            'mail' => $customer->email,
        ];
        
        return view('customer_followups.create', compact('customer', 'prefill'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $customer_id)
    {
        $customer = Customer::findOrFail($customer_id);
        $validated = $request->validate([
            'track_date' => 'required|date',
            'note' => 'required|string',
            'status' => CustomerFollowup::getStatusValidationRule(),
            'conversation_type' => 'required|in:telefon,yerinde,bayide',
            'pluscard_been_loaded' => 'required|in:0,1',
            'number_of_customers_loaded' => 'nullable|required_if:pluscard_been_loaded,1|integer|min:1',
            'loading_amount' => 'nullable|required_if:pluscard_been_loaded,1|numeric|min:0',
            'reason_not_understanding' => 'nullable|required_if:pluscard_been_loaded,0|string',
            'contact_first_name' => 'nullable|string|max:100',
            'contact_last_name' => 'nullable|string|max:100',
            'contact_title' => 'nullable|string|max:100',
            'contact_phone' => 'nullable|string|max:30',
            'contact_email' => 'nullable|email|max:100',
            'current_branch_count' => 'nullable|integer|min:0',
            'branch_potential' => 'nullable|integer|min:0',
            'agreement_status' => 'nullable|in:0,1',
            'city' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            'branch_name' => 'nullable|string|max:255',
            'meet_date' => 'nullable|date',
            'user_name' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'phone_1' => 'nullable|string|max:30',
            'phone_2' => 'nullable|string|max:30',
            'phone_3' => 'nullable|string|max:30',
            'mail' => 'nullable|email|max:100',
            'work_type' => CustomerFollowup::getWorkTypeValidationRule(),
            'current_firm' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status_at_meeting_date' => CustomerFollowup::getStatusAtMeetingDateValidationRule(),
        ]);
        $validated['customer_id'] = $customer->id;
        CustomerFollowup::create($validated);
        return redirect()->route('customers.customer-followups.index', $customer->id)->with('success', 'Takip kaydı eklendi!');
    }

    /**
     * Display the specified resource.
     */
    public function show($customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($id);
        return view('customer_followups.show', compact('customer', 'followup'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($id);
        return view('customer_followups.edit', compact('customer', 'followup'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($id);
        $validated = $request->validate([
            'track_date' => 'required|date',
            'note' => 'required|string',
            'status' => CustomerFollowup::getStatusValidationRule(),
            'conversation_type' => 'required|in:telefon,yerinde,bayide',
            'pluscard_been_loaded' => 'required|in:0,1',
            'number_of_customers_loaded' => 'nullable|required_if:pluscard_been_loaded,1|integer|min:1',
            'loading_amount' => 'nullable|required_if:pluscard_been_loaded,1|numeric|min:0',
            'reason_not_understanding' => 'nullable|required_if:pluscard_been_loaded,0|string',
            'contact_first_name' => 'nullable|string|max:100',
            'contact_last_name' => 'nullable|string|max:100',
            'contact_title' => 'nullable|string|max:100',
            'contact_phone' => 'nullable|string|max:30',
            'contact_email' => 'nullable|email|max:100',
            'current_branch_count' => 'nullable|integer|min:0',
            'branch_potential' => 'nullable|integer|min:0',
            'agreement_status' => 'nullable|in:0,1',
            'city' => 'nullable|string|max:100',
            'district' => 'nullable|string|max:100',
            'branch_name' => 'nullable|string|max:255',
            'meet_date' => 'nullable|date',
            'user_name' => 'nullable|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'phone_1' => 'nullable|string|max:30',
            'phone_2' => 'nullable|string|max:30',
            'phone_3' => 'nullable|string|max:30',
            'mail' => 'nullable|email|max:100',
            'work_type' => CustomerFollowup::getWorkTypeValidationRule(),
            'current_firm' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status_at_meeting_date' => CustomerFollowup::getStatusAtMeetingDateValidationRule(),
        ]);
        $followup->update($validated);
        return redirect()->route('customers.customer-followups.index', $customer->id)->with('success', 'Takip kaydı güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($customer_id, $id)
    {
        $customer = Customer::findOrFail($customer_id);
        $followup = CustomerFollowup::where('customer_id', $customer->id)->findOrFail($id);
        $followup->delete();
        return redirect()->route('customers.customer-followups.index', $customer->id)->with('success', 'Takip kaydı silindi!');
    }
}
