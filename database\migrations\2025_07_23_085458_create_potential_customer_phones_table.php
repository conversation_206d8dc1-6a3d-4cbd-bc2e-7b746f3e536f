<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('potential_customer_phones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('potential_customer_id')->constrained()->onDelete('cascade');
            $table->string('phone');
            $table->string('type')->default('Sabit'); // Sabit, Mobil, Fax, Diğer
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('potential_customer_phones');
    }
};
