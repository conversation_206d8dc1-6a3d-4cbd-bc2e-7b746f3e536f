@extends('layouts.index')

@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>Müşteri Listesi</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Ana Sayfa</a></li>
                    <li class="breadcrumb-item active">Müşteri Listesi</li>
                </ol>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</section>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="{{ route('customers.create') }}" class="btn btn-primary"><PERSON>ni <PERSON></a>
                        </h3>

                        <div class="card-tools">
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            <form method="GET" action="" class="d-flex">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="q" class="form-control float-right" placeholder="telefon,isim,şirket" value="{{ request('q') }}">

                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-default"><i class="fas fa-search"></i></button>
                                    </div>
                                </div>
                                <div class="input-group input-group-sm">
                                    <span class="align-self-center" style="margin-left:8px;">Sayfa başına kayıt:</span>
                                    <select name="perPage" class="form-select" style="width: auto; max-width: 120px;" onchange="this.form.submit()">
                                        <option value="5" {{ request('perPage') == 5 ? 'selected' : '' }}>5</option>
                                        <option value="10" {{ request('perPage', 10) == 10 ? 'selected' : '' }}>10</option>
                                        <option value="25" {{ request('perPage') == 25 ? 'selected' : '' }}>25</option>
                                        <option value="50" {{ request('perPage') == 50 ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ request('perPage') == 100 ? 'selected' : '' }}>100</option>
                                    </select>
                                </div>
                                @if(request('q'))
                                    <a href="{{ route('customers.index') }}" class="btn btn-outline-danger">Temizle</a>
                                @endif
                            </form>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0" style="height: 100%;">
                        <table class="table table-head-fixed text-nowrap table-bordered table-striped">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Email</th>
                                <th>Yetkili Unvan</th>
                                <th>Yetkili İsim</th>
                                <th>Yetkili Soyisim</th>
                                <th>Yetkili Telefon</th>
                                <th>Şirket İsmi</th>
                                <th>Şirket Telefonları</th>
                                <th>İl</th>
                                <th>İlçe</th>
                                <th>Adres</th>
                                <th>Kayıt Tarihi</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($customers as $customer)
                                <tr>
                                    <td>{{ $customer->id }}</td>
                                    <td>{{ $customer->email }}</td>
                                    <td>{{ $customer->authorized_title }}</td>
                                    <td>{{ $customer->authorized_first_name }}</td>
                                    <td>{{ $customer->authorized_last_name }}</td>
                                    <td>{{ $customer->authorized_phone }}</td>
                                    <td>{{ $customer->company_name }}</td>
                                    <td>
                                        @php
                                            $allPhones = collect();

                                            // Yeni sistemden telefonları al
                                            if(method_exists($customer, 'phones') && $customer->phones) {
                                                $allPhones = $customer->phones;
                                            }

                                            // Eğer yeni sistemde telefon yoksa, eski sistemden al
                                            if($allPhones->count() == 0) {
                                                if($customer->phone_1) $allPhones->push((object)['phone' => $customer->phone_1, 'type' => 'Sabit']);
                                                if($customer->phone_2) $allPhones->push((object)['phone' => $customer->phone_2, 'type' => 'Sabit']);
                                                if($customer->phone_3) $allPhones->push((object)['phone' => $customer->phone_3, 'type' => 'Sabit']);
                                            }
                                        @endphp

                                        @if($allPhones->count() > 0)
                                            @foreach($allPhones as $index => $phone)
                                                <small class="d-block">
                                                    <span class="badge badge-{{ $phone->type == 'Mobil' ? 'success' : ($phone->type == 'Fax' ? 'warning' : 'primary') }} me-1">{{ $phone->type }}</span>
                                                    {{ $phone->phone }}
                                                </small>
                                                @if($index < $allPhones->count() - 1)<br>@endif
                                            @endforeach
                                        @else
                                            <small class="text-muted">Telefon yok</small>
                                        @endif
                                    </td>
                                    <td>{{ $customer->city }}</td>
                                    <td>{{ $customer->district }}</td>
                                    <td>{{ $customer->address }}</td>
                                    <td>{{ $customer->created_at }}</td>
                                    <td>
                                        <a href="{{ route('customers.show', $customer->id) }}" class="btn btn-info btn-sm" title="Detay">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('customers.edit', $customer->id) }}" class="btn btn-warning btn-sm" title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('customers.customer-followups.index', $customer->id) }}" class="btn btn-secondary btn-sm" title="Takip Formu">
                                            <i class="fas fa-clipboard-list"></i> Takip
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer clearfix">
                        {{ $customers->links('pagination::bootstrap-4') }}
                    </div>
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
