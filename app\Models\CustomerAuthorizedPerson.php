<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerAuthorizedPerson extends Model
{
    protected $table = 'customer_authorized_persons';

    protected $fillable = [
        'customer_id',
        'title',
        'first_name',
        'last_name',
        'phone',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }
}
