<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Şirket Telefon Çoklu Ekleme Testi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8/jquery.inputmask.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Şirket Telefon Çoklu Ekleme Özelliği Testi</h4>
                    </div>
                    <div class="card-body">
                        <form>
                            <!-- Şirket Telefonları -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">Şirket Telefonları</h6>
                                    <button type="button" class="btn btn-sm btn-primary" id="add-company-phone">
                                        <i class="fas fa-plus"></i> Telefon Ekle
                                    </button>
                                </div>
                                
                                <div id="company-phones-container">
                                    <!-- İlk telefon alanı varsayılan olarak gösterilir -->
                                    <div class="company-phone-item border p-3 mb-3 rounded" data-index="0">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Şirket Telefon #1</h6>
                                            <button type="button" class="btn btn-sm btn-danger remove-company-phone" style="display: none;">
                                                <i class="fas fa-trash"></i> Kaldır
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Telefon Numarası</label>
                                                    <input type="text" class="form-control inputmask" name="company_phones[0][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Telefon Türü</label>
                                                    <select class="form-control" name="company_phones[0][type]">
                                                        <option value="Sabit">Sabit Hat</option>
                                                        <option value="Mobil">Mobil</option>
                                                        <option value="Fax">Fax</option>
                                                        <option value="Diğer">Diğer</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="button" class="btn btn-success" onclick="showFormData()">
                                    <i class="fas fa-eye"></i> Form Verilerini Göster
                                </button>
                            </div>
                        </form>

                        <div id="form-data" class="mt-4" style="display: none;">
                            <h6>Form Verileri:</h6>
                            <pre id="form-data-content" class="bg-light p-3 rounded"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Input mask uygula
            $(".inputmask").inputmask({
                mask: "+99 999 999 99 99",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                clearIncomplete: true,
                definitions: {
                    "9": {
                        validator: "[0-9]",
                        cardinality: 1,
                        definitionSymbol: "9"
                    }
                },
                onBeforePaste: function (pastedValue, opts) {
                    return pastedValue.replace(/[^\d\+]/g, '');
                },
                onKeyDown: function(e, buffer, caretPos, opts) {
                    var key = e.key;
                    if (!/[0-9]/.test(key) && key.length === 1) {
                        e.preventDefault();
                    }
                }
            });

            let companyPhoneIndex = 1;

            function updatePhoneRemoveButtons() {
                const items = $('.company-phone-item');
                if (items.length <= 1) {
                    $('.remove-company-phone').hide();
                } else {
                    $('.remove-company-phone').show();
                }
            }

            function updatePhoneNumbers() {
                $('.company-phone-item').each(function(index) {
                    $(this).find('h6').text('Şirket Telefon #' + (index + 1));
                    $(this).attr('data-index', index);
                    
                    // Input name'lerini güncelle
                    $(this).find('input[name*="[phone]"]').attr('name', 'company_phones[' + index + '][phone]');
                    $(this).find('select[name*="[type]"]').attr('name', 'company_phones[' + index + '][type]');
                });
            }

            // Şirket telefonu ekleme
            $('#add-company-phone').on('click', function() {
                const container = $('#company-phones-container');
                const newItem = `
                    <div class="company-phone-item border p-3 mb-3 rounded" data-index="${companyPhoneIndex}">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Şirket Telefon #${companyPhoneIndex + 1}</h6>
                            <button type="button" class="btn btn-sm btn-danger remove-company-phone">
                                <i class="fas fa-trash"></i> Kaldır
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Telefon Numarası</label>
                                    <input type="text" class="form-control inputmask" name="company_phones[${companyPhoneIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Telefon Türü</label>
                                    <select class="form-control" name="company_phones[${companyPhoneIndex}][type]">
                                        <option value="Sabit">Sabit Hat</option>
                                        <option value="Mobil">Mobil</option>
                                        <option value="Fax">Fax</option>
                                        <option value="Diğer">Diğer</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                container.append(newItem);

                // Yeni eklenen telefon alanına inputmask uygula
                container.find('.inputmask').last().inputmask({
                    mask: "+99 999 999 99 99",
                    showMaskOnHover: false,
                    showMaskOnFocus: true,
                    clearIncomplete: true
                });

                companyPhoneIndex++;
                updatePhoneRemoveButtons();
            });

            // Şirket telefonu kaldırma
            $(document).on('click', '.remove-company-phone', function() {
                $(this).closest('.company-phone-item').remove();
                updatePhoneRemoveButtons();
                updatePhoneNumbers();
            });

            // Sayfa yüklendiğinde remove butonlarını güncelle
            updatePhoneRemoveButtons();
        });

        function showFormData() {
            const formData = {};
            $('form').find('input, select').each(function() {
                const name = $(this).attr('name');
                const value = $(this).val();
                if (name && value) {
                    formData[name] = value;
                }
            });
            
            $('#form-data-content').text(JSON.stringify(formData, null, 2));
            $('#form-data').show();
        }
    </script>
</body>
</html>
