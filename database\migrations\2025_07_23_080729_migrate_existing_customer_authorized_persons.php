<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Customer;
use App\Models\CustomerAuthorizedPerson;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mevcut customers tablosundaki yetkili kişi bilgilerini
        // customer_authorized_persons tablosuna taşı
        $customers = Customer::whereNotNull('authorized_title')
            ->orWhereNotNull('authorized_first_name')
            ->orWhereNotNull('authorized_last_name')
            ->orWhereNotNull('authorized_phone')
            ->get();

        foreach ($customers as $customer) {
            // En az bir yetkili kişi bilgisi varsa kaydet
            if ($customer->authorized_title ||
                $customer->authorized_first_name ||
                $customer->authorized_last_name ||
                $customer->authorized_phone) {

                CustomerAuthorizedPerson::create([
                    'customer_id' => $customer->id,
                    'title' => $customer->authorized_title,
                    'first_name' => $customer->authorized_first_name,
                    'last_name' => $customer->authorized_last_name,
                    'phone' => $customer->authorized_phone,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Geri alma işlemi - customer_authorized_persons tablosundaki
        // verileri customers tablosuna geri taşı
        $authorizedPersons = CustomerAuthorizedPerson::all();

        foreach ($authorizedPersons as $person) {
            $customer = Customer::find($person->customer_id);
            if ($customer) {
                $customer->update([
                    'authorized_title' => $person->title,
                    'authorized_first_name' => $person->first_name,
                    'authorized_last_name' => $person->last_name,
                    'authorized_phone' => $person->phone,
                ]);
            }
        }

        // Tüm yetkili kişi kayıtlarını sil
        CustomerAuthorizedPerson::truncate();
    }
};
