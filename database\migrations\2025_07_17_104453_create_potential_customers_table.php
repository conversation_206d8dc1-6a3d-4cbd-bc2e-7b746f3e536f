<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('potential_customers', function (Blueprint $table) {
            $table->id();
            $table->string('company_name');
            $table->string('authorized_title')->nullable();
            $table->string('authorized_name')->nullable();
            $table->string('authorized_lastname')->nullable();
            $table->string('authorized_phone')->nullable();
            $table->string('phone_1')->nullable();
            $table->string('phone_2')->nullable();
            $table->string('phone_3')->nullable();
            $table->string('city');
            $table->string('district');
            $table->text('address');
            $table->enum('offer_status', ['new', 'pending', 'accepted', 'rejected', 'customer'])->default('new');
            $table->dateTime('offer_date')->nullable();
            $table->boolean('is_working_with_us')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('potential_customers');
    }
};
