<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Customer;
use App\Models\CustomerPhone;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mevcut customers tablosundaki telefon bilgilerini
        // customer_phones tablosuna taşı
        $customers = Customer::whereNotNull('phone_1')
            ->orWhereNotNull('phone_2')
            ->orWhereNotNull('phone_3')
            ->get();

        foreach ($customers as $customer) {
            // phone_1 varsa ekle
            if (!empty($customer->phone_1)) {
                CustomerPhone::create([
                    'customer_id' => $customer->id,
                    'phone' => $customer->phone_1,
                    'type' => 'Sabit',
                ]);
            }

            // phone_2 varsa ekle
            if (!empty($customer->phone_2)) {
                CustomerPhone::create([
                    'customer_id' => $customer->id,
                    'phone' => $customer->phone_2,
                    'type' => 'Sabit',
                ]);
            }

            // phone_3 varsa ekle
            if (!empty($customer->phone_3)) {
                CustomerPhone::create([
                    'customer_id' => $customer->id,
                    'phone' => $customer->phone_3,
                    'type' => 'Sabit',
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Geri alma işlemi - customer_phones tablosundaki
        // verileri customers tablosuna geri taşı
        $customers = Customer::all();

        foreach ($customers as $customer) {
            $phones = $customer->phones()->orderBy('id')->get();
            
            if ($phones->count() > 0) {
                $customer->update(['phone_1' => $phones[0]->phone ?? null]);
            }
            if ($phones->count() > 1) {
                $customer->update(['phone_2' => $phones[1]->phone ?? null]);
            }
            if ($phones->count() > 2) {
                $customer->update(['phone_3' => $phones[2]->phone ?? null]);
            }
        }

        // Tüm telefon kayıtlarını sil
        CustomerPhone::truncate();
    }
};
