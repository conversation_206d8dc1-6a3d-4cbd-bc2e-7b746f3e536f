<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PotentialCustomer;
use App\Models\PotentialCustomerPhone;

class PotentialCustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $perPage = request('perPage', 10);
        $potentialCustomers = PotentialCustomer::with(['authorizedPersons', 'phones'])
            ->search(request('q'))
            ->orderBy('id', 'desc')
            ->paginate($perPage)
            ->appends(request()->query());

        return view('potential_customers.index', compact('potentialCustomers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('potential_customers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'authorized_persons' => 'required|array|min:1',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.name' => 'nullable|string|max:255',
            'authorized_persons.*.lastname' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|max:20',
            'phone_1' => 'nullable|string|max:20',
            'phone_2' => 'nullable|string|max:20',
            'phone_3' => 'nullable|string|max:20',
            'city' => 'required|string|max:100',
            'district' => 'required|string|max:100',
            'address' => 'required|string',
            'offer_status' => 'required|in:new,pending,accepted,rejected,customer',
            'offer_date' => 'nullable|date',
            'is_working_with_us' => 'required|boolean',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons'], $validated['company_phones']);

        $potentialCustomer = PotentialCustomer::create($validated);

        // Yetkili kişileri ekle
        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['name'] ?? '')) ||
                !empty(trim($person['lastname'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $potentialCustomer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'name' => trim($person['name'] ?? ''),
                    'lastname' => trim($person['lastname'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını ekle
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                $potentialCustomer->phones()->create([
                    'phone' => trim($phone['phone']),
                    'type' => trim($phone['type'] ?? 'Sabit'),
                ]);
            }
        }

        return redirect()->route('potential-customers.index')->with('success', 'Potansiyel müşteri başarıyla eklendi!');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $potentialCustomer = PotentialCustomer::with(['authorizedPersons', 'phones'])->findOrFail($id);
        return view('potential_customers.show', compact('potentialCustomer'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $potentialCustomer = PotentialCustomer::with(['authorizedPersons', 'phones'])->findOrFail($id);
        return view('potential_customers.edit', compact('potentialCustomer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PotentialCustomer $potentialCustomer)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'authorized_persons' => 'required|array|min:1',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.name' => 'nullable|string|max:255',
            'authorized_persons.*.lastname' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|max:20',
            'company_phones.*.id' => 'nullable|integer',
            'phone_1' => 'nullable|string|max:20',
            'phone_2' => 'nullable|string|max:20',
            'phone_3' => 'nullable|string|max:20',
            'city' => 'required|string|max:100',
            'district' => 'required|string|max:100',
            'address' => 'required|string',
            'offer_status' => 'required|in:new,pending,accepted,rejected,customer',
            'offer_date' => 'nullable|date',
            'is_working_with_us' => 'required|boolean',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons'], $validated['company_phones']);

        $potentialCustomer->update($validated);

        // Yetkili kişileri güncelle
        $potentialCustomer->authorizedPersons()->delete();

        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['name'] ?? '')) ||
                !empty(trim($person['lastname'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $potentialCustomer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'name' => trim($person['name'] ?? ''),
                    'lastname' => trim($person['lastname'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını güncelle
        $existingPhoneIds = [];
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                if (!empty($phone['id'])) {
                    // Mevcut telefonu güncelle
                    $existingPhone = $potentialCustomer->phones()->find($phone['id']);
                    if ($existingPhone) {
                        $existingPhone->update([
                            'phone' => trim($phone['phone']),
                            'type' => trim($phone['type'] ?? 'Sabit'),
                        ]);
                        $existingPhoneIds[] = $phone['id'];
                    }
                } else {
                    // Yeni telefon ekle
                    $newPhone = $potentialCustomer->phones()->create([
                        'phone' => trim($phone['phone']),
                        'type' => trim($phone['type'] ?? 'Sabit'),
                    ]);
                    $existingPhoneIds[] = $newPhone->id;
                }
            }
        }

        // Silinmiş telefonları kaldır
        $potentialCustomer->phones()->whereNotIn('id', $existingPhoneIds)->delete();

        return redirect()->route('potential-customers.index')->with('success', 'Potansiyel müşteri başarıyla güncellendi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $potentialCustomer = PotentialCustomer::findOrFail($id);
        $potentialCustomer->delete();
        return redirect()->route('potential-customers.index')->with('success', 'Potansiyel müşteri silindi!');
    }
}









